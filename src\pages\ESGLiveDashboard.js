import React, { useState, useEffect, useRef } from 'react';
import { TabView, TabPanel } from 'primereact/tabview';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { Menu } from 'primereact/menu';
import { Card } from 'primereact/card';

import { useSelector } from 'react-redux';
import APIServices from '../service/APIService';
import { API } from '../components/constants/api_url';
import * as XLSX from 'xlsx';
import ESGChart, { ESGChartConfigs, ESGColorSchemes } from '../components/charts/ESGChart';

const ESGLiveDashboard = () => {
    const [activeIndex, setActiveIndex] = useState(0);
    const [filters, setFilters] = useState({
        country: null,
        businessUnit: null,
        site: null,
        year: 2024
    });

    // User access control - RBAC implementation
    const [userAccess, setUserAccess] = useState({
        type: 'internal', // 'internal' or 'external'
        permissions: {
            canView: true,
            canDownload: true,
            canShare: true
        }
    });

    // Filter options
    const [countryOptions, setCountryOptions] = useState([]);
    const [businessUnitOptions, setBusinessUnitOptions] = useState([]);
    const [siteOptions, setSiteOptions] = useState([]);
    const [yearOptions, setYearOptions] = useState([
        { label: '2024', value: 2024 },
        { label: '2023', value: 2023 },
        { label: '2022', value: 2022 },
        { label: '2021', value: 2021 },
        { label: '2020', value: 2020 }
    ]);

    // Chart data states
    const [chartData, setChartData] = useState({
        highlights: {},
        environment: {},
        social: {},
        governance: {}
    });

    // Chart refs for cleanup
    const chartRefs = useRef({});

    const selector = useSelector(state => state.user.userdetail);

    useEffect(() => {
        loadFilterOptions();
        loadChartData();
        // Initialize user access based on role
        if (selector && selector.role) {
            setUserAccess(prev => ({
                ...prev,
                type: selector.role === 'external' ? 'external' : 'internal',
                permissions: {
                    canView: true,
                    canDownload: selector.role !== 'external' || selector.permissions?.canDownload,
                    canShare: selector.role !== 'external' || selector.permissions?.canShare
                }
            }));
        }
    }, [selector]);

    useEffect(() => {
        loadChartData();
    }, [filters]);

    const loadFilterOptions = async () => {
        try {
            // Load countries/entities
            const countriesResponse = await APIServices.get(API.LocationThree);
            const countries = countriesResponse.data.map(item => ({
                label: item.name,
                value: item.id
            }));
            setCountryOptions(countries);

            // Load business units
            const businessUnitsResponse = await APIServices.get(API.LocationTwo_UP(1));
            const businessUnits = businessUnitsResponse.data.map(item => ({
                label: item.name,
                value: item.id
            }));
            setBusinessUnitOptions(businessUnits);

            // Load sites
            const sitesResponse = await APIServices.get(API.LocationOne_UP(1));
            const sites = sitesResponse.data.map(item => ({
                label: item.name,
                value: item.id
            }));
            setSiteOptions(sites);
        } catch (error) {
            console.error('Error loading filter options:', error);
        }
    };

    const loadChartData = async () => {
        try {
            // This will be implemented with actual API calls based on the specifications
            // For now, using mock data structure
            const mockData = {
                highlights: {
                    carbonIntensity: generateMockChartData('Carbon Intensity'),
                    carbonFootprint: generateMockChartData('Scope 1+2 Market Based (Carbon Footprint)', 'comparison'),
                    renewableEnergyFactor: generateMockChartData('Renewable Energy Factor (REF)', 'comparison'),
                    powerUsageEffectiveness: generateMockChartData('Power Usage Effectiveness (PUE)'),
                    greenDataCentre: generateMockChartData('Green Data Centre'),
                    waterUsageEffectiveness: generateMockChartData('Water Usage Effectiveness (WUE)'),
                    safety: generateMockChartData('Safety'),
                    diversity: generateMockChartData('Diversity'),
                    greenFinancing: generateMockChartData('Green Financing'),
                    antiCorruption: generateMockChartData('Anti-Corruption')
                },
                environment: {
                    scope1And2Emissions: generateMockChartData('Scope 1+2 GHG Emissions (Target vs Actual)', 'comparison'),
                    electricityConsumption: generateMockChartData('Electricity Consumption - Renewable vs Non-renewable', 'comparison'),
                    carbonIntensity: generateMockChartData('Carbon Intensity'),
                    renewablesBreakdown: generateMockChartData('Renewables Breakdown by Type', 'pie'),
                    ref: generateMockChartData('Renewable Energy Factor (REF)', 'comparison'),
                    pue: generateMockChartData('Power Usage Effectiveness (PUE)'),
                    wue: generateMockChartData('Water Usage Effectiveness (WUE)'),
                    waterWithdrawn: generateMockChartData('Water Withdrawn'),
                    nonHazardousWaste: generateMockChartData('Non-Hazardous Waste Generated'),
                    hazardousWaste: generateMockChartData('Hazardous Waste Generated')
                },
                social: {
                    constructionHours: generateMockChartData('Construction & Operations Hours vs TRIR'),
                    recordableIncidents: generateMockChartData('Number of Recordable Incidents'),
                    genderDiversity: generateMockChartData('Gender Diversity (Women %)'),
                    womenInLeadership: generateMockChartData('Percentage of Women in Leadership'),
                    newHiresAndTurnover: generateMockChartData('New Hires and Turnover')
                },
                governance: {
                    // Governance charts will be added when DCF forms are ready
                }
            };
            setChartData(mockData);
        } catch (error) {
            console.error('Error loading chart data:', error);
        }
    };

    const generateMockChartData = (title, type = 'bar') => {
        const years = ['2020', '2021', '2022', '2023', '2024'];

        if (type === 'comparison') {
            // Generate realistic data for comparison charts
            const actualData = [678, 597, 428, 317, 280]; // Decreasing trend for carbon intensity
            const targetData = [650, 550, 400, 300, 250]; // Target values

            return {
                title,
                labels: years,
                datasets: [
                    {
                        label: 'Actual',
                        data: actualData,
                        backgroundColor: ESGColorSchemes.comparison[0],
                        borderColor: ESGColorSchemes.comparison[0],
                        borderWidth: 1
                    },
                    {
                        label: 'Target',
                        data: targetData,
                        backgroundColor: ESGColorSchemes.comparison[1],
                        borderColor: ESGColorSchemes.comparison[1],
                        borderWidth: 1
                    }
                ]
            };
        } else if (type === 'pie') {
            return {
                title,
                labels: ['Solar', 'Wind', 'Hydro', 'Biomass'],
                datasets: [{
                    data: [40, 30, 20, 10],
                    backgroundColor: ESGColorSchemes.renewable,
                    borderWidth: 1
                }]
            };
        } else if (title.includes('PUE')) {
            // PUE values should be around 1.0-2.0
            return {
                title,
                labels: years,
                datasets: [{
                    label: title,
                    data: [1.68, 1.54, 1.52, 1.48, 1.45],
                    backgroundColor: ESGColorSchemes.primary[0],
                    borderColor: ESGColorSchemes.primary[0],
                    borderWidth: 1
                }]
            };
        } else if (title.includes('WUE')) {
            // WUE values
            return {
                title,
                labels: years,
                datasets: [{
                    label: title,
                    data: [1.11, 1.04, 0.95, 0.88, 0.82],
                    backgroundColor: ESGColorSchemes.primary[0],
                    borderColor: ESGColorSchemes.primary[0],
                    borderWidth: 1
                }]
            };
        } else if (title.includes('REF') || title.includes('Renewable')) {
            // REF percentage values
            return {
                title,
                labels: years,
                datasets: [{
                    label: title,
                    data: [33, 44, 52, 65, 72],
                    backgroundColor: ESGColorSchemes.environment[0],
                    borderColor: ESGColorSchemes.environment[0],
                    borderWidth: 1
                }]
            };
        } else {
            return {
                title,
                labels: years,
                datasets: [{
                    label: title,
                    data: Array.from({ length: 5 }, () => Math.floor(Math.random() * 1000) + 100),
                    backgroundColor: ESGColorSchemes.primary[0],
                    borderColor: ESGColorSchemes.primary[0],
                    borderWidth: 1
                }]
            };
        }
    };

    const handleFilterChange = (filterType, value) => {
        setFilters(prev => ({
            ...prev,
            [filterType]: value
        }));
    };

    const handleShare = () => {
        // Implement share functionality
        console.log('Share dashboard');
    };

    const handleDownload = (format = 'excel') => {
        try {
            if (format === 'excel') {
                // Create workbook
                const wb = XLSX.utils.book_new();

                // Add highlights data
                const highlightsData = Object.entries(chartData.highlights).map(([key, data]) => ({
                    Chart: data.title,
                    '2020': data.datasets[0].data[0],
                    '2021': data.datasets[0].data[1],
                    '2022': data.datasets[0].data[2],
                    '2023': data.datasets[0].data[3],
                    '2024': data.datasets[0].data[4]
                }));

                const highlightsWS = XLSX.utils.json_to_sheet(highlightsData);
                XLSX.utils.book_append_sheet(wb, highlightsWS, 'Highlights');

                // Add environment data
                const environmentData = Object.entries(chartData.environment).map(([key, data]) => ({
                    Chart: data.title,
                    '2020': data.datasets[0].data[0],
                    '2021': data.datasets[0].data[1],
                    '2022': data.datasets[0].data[2],
                    '2023': data.datasets[0].data[3],
                    '2024': data.datasets[0].data[4]
                }));

                const environmentWS = XLSX.utils.json_to_sheet(environmentData);
                XLSX.utils.book_append_sheet(wb, environmentWS, 'Environment');

                // Download file
                XLSX.writeFile(wb, `ESG_Dashboard_${new Date().toISOString().split('T')[0]}.xlsx`);
            }
        } catch (error) {
            console.error('Error downloading data:', error);
        }
    };

    const downloadMenuRef = useRef(null);
    const downloadMenuItems = [
        {
            label: 'Excel',
            icon: 'pi pi-file-excel',
            command: () => handleDownload('excel')
        }
    ];

    const renderFilters = (showAllFilters = true) => (
        <div className="flex align-items-center gap-3 mb-4">
            {showAllFilters && (
                <>
                    <div className="flex align-items-center gap-2">
                        <label className="text-bold fs-12">Country:</label>
                        <Dropdown
                            value={filters.country}
                            options={countryOptions}
                            onChange={(e) => handleFilterChange('country', e.value)}
                            placeholder="United States"
                            className="filter-dropdown"
                            style={{ minWidth: '150px' }}
                        />
                    </div>
                    <div className="flex align-items-center gap-2">
                        <label className="text-bold fs-12">Business Unit:</label>
                        <Dropdown
                            value={filters.businessUnit}
                            options={businessUnitOptions}
                            onChange={(e) => handleFilterChange('businessUnit', e.value)}
                            placeholder="Manufacturing"
                            className="filter-dropdown"
                            style={{ minWidth: '150px' }}
                        />
                    </div>
                    <div className="flex align-items-center gap-2">
                        <label className="text-bold fs-12">Site:</label>
                        <Dropdown
                            value={filters.site}
                            options={siteOptions}
                            onChange={(e) => handleFilterChange('site', e.value)}
                            placeholder="Manufacturing Plant 1"
                            className="filter-dropdown"
                            style={{ minWidth: '180px' }}
                        />
                    </div>
                </>
            )}
            <div className="flex align-items-center gap-2">
                <label className="text-bold fs-12">Year:</label>
                <Dropdown
                    value={filters.year}
                    options={yearOptions}
                    onChange={(e) => handleFilterChange('year', e.value)}
                    placeholder="2024"
                    className="filter-dropdown"
                    style={{ minWidth: '100px' }}
                />
            </div>
            <div className="flex gap-2 ml-auto">
                {userAccess.permissions.canShare && (
                    <Button
                        label="Share"
                        icon="pi pi-share-alt"
                        className="p-button-outlined"
                        onClick={handleShare}
                    />
                )}
                {userAccess.permissions.canDownload && (
                    <Button
                        label="Download"
                        icon="pi pi-download"
                        onClick={() => handleDownload('excel')}
                    />
                )}
                <Menu model={downloadMenuItems} popup ref={downloadMenuRef} />
            </div>
        </div>
    );

    return (
        <div className="bg-smoke font-lato" style={{ padding: '20px', paddingTop: '90px' }}>
            <div className="col-12">
                {/* Header */}
                <div className="mb-4">
                    <div className="flex align-items-center justify-content-between">
                        <div>
                            <h2 className="text-big-one clr-navy fs-24 mb-2">ESG Live Dashboard</h2>
                            <p className="text-micro clr-navy fs-14">
                                Comprehensive view of Environmental, Social, and Governance metrics
                            </p>
                        </div>
                        <div className="flex align-items-center gap-3">
                            {/* User Type Selector */}
                            <div className="flex align-items-center gap-2">
                                <label className="text-bold fs-12 clr-navy">Access Type:</label>
                                <Dropdown
                                    value={userAccess.type}
                                    options={[
                                        { label: 'Internal User', value: 'internal' },
                                        { label: 'External User', value: 'external' }
                                    ]}
                                    onChange={(e) => setUserAccess(prev => ({
                                        ...prev,
                                        type: e.value,
                                        permissions: {
                                            canView: true,
                                            canDownload: e.value === 'internal',
                                            canShare: e.value === 'internal'
                                        }
                                    }))}
                                    className="filter-dropdown"
                                    style={{ minWidth: '140px' }}
                                />
                            </div>

                            {/* User Info */}
                            <div className="flex align-items-center gap-2">
                                <span className="text-bold fs-12 clr-navy">
                                    {selector?.name || 'John Doe'} - Sustainability Manager
                                </span>
                                <div className="user-avatar bg-primary text-white border-circle flex align-items-center justify-content-center"
                                     style={{ width: '32px', height: '32px', fontSize: '12px' }}>
                                    {(selector?.name || 'John Doe').split(' ').map(n => n[0]).join('')}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                {/* Filters - Show all filters except for Highlights section */}
                {activeIndex !== 0 && renderFilters()}

                {/* Tab Navigation */}
                <TabView activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)}>
                    <TabPanel header="Highlights">
                        <div className="grid">
                            {/* Highlights section only has year filter as specified */}
                            <div className="col-12 mb-3">
                                <div className="flex align-items-center gap-2">
                                    <label className="text-bold fs-12">Year:</label>
                                    <Dropdown
                                        value={filters.year}
                                        options={yearOptions}
                                        onChange={(e) => handleFilterChange('year', e.value)}
                                        placeholder="2024"
                                        className="filter-dropdown"
                                        style={{ minWidth: '100px' }}
                                    />
                                </div>
                            </div>

                            {/* Row 1 */}
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="carbon-intensity-highlights"
                                        type="bar"
                                        data={chartData.highlights.carbonIntensity}
                                        {...ESGChartConfigs.carbonIntensity}
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="carbon-footprint-highlights"
                                        type="bar"
                                        data={chartData.highlights.carbonFootprint}
                                        {...ESGChartConfigs.carbonFootprint}
                                        height={300}
                                    />
                                </Card>
                            </div>

                            {/* Row 2 */}
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="ref-highlights"
                                        type="bar"
                                        data={chartData.highlights.renewableEnergyFactor}
                                        {...ESGChartConfigs.renewableEnergyFactor}
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="pue-highlights"
                                        type="bar"
                                        data={chartData.highlights.powerUsageEffectiveness}
                                        {...ESGChartConfigs.powerUsageEffectiveness}
                                        height={300}
                                    />
                                </Card>
                            </div>

                            {/* Row 3 */}
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="wue-highlights"
                                        type="bar"
                                        data={chartData.highlights.waterUsageEffectiveness}
                                        {...ESGChartConfigs.waterUsageEffectiveness}
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="green-data-centre-highlights"
                                        type="bar"
                                        data={chartData.highlights.greenDataCentre}
                                        title="Green Data Centre"
                                        height={300}
                                    />
                                </Card>
                            </div>

                            {/* Row 4: Safety & Diversity */}
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="safety-highlights"
                                        type="bar"
                                        data={chartData.highlights.safety}
                                        title="Safety"
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="diversity-highlights"
                                        type="bar"
                                        data={chartData.highlights.diversity}
                                        title="Diversity"
                                        height={300}
                                    />
                                </Card>
                            </div>

                            {/* Row 5: Green Financing & Anti-Corruption */}
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="green-financing-highlights"
                                        type="bar"
                                        data={chartData.highlights.greenFinancing}
                                        title="Green Financing"
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="anti-corruption-highlights"
                                        type="bar"
                                        data={chartData.highlights.antiCorruption}
                                        title="Anti-Corruption"
                                        height={300}
                                    />
                                </Card>
                            </div>
                        </div>
                    </TabPanel>
                    
                    <TabPanel header="Environment">
                        <div className="grid">
                            {/* Row 1: Scope 1+2 GHG Emissions & Electricity Consumption */}
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="scope1-2-emissions-env"
                                        type="bar"
                                        data={chartData.environment.scope1And2Emissions}
                                        {...ESGChartConfigs.scope1And2Emissions}
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="electricity-consumption-env"
                                        type="bar"
                                        data={chartData.environment.electricityConsumption}
                                        {...ESGChartConfigs.electricityConsumption}
                                        height={300}
                                    />
                                </Card>
                            </div>

                            {/* Row 2: Carbon Intensity & Renewables Breakdown */}
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="carbon-intensity-env"
                                        type="bar"
                                        data={chartData.environment.carbonIntensity}
                                        {...ESGChartConfigs.carbonIntensity}
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="renewables-breakdown-env"
                                        type="pie"
                                        data={chartData.environment.renewablesBreakdown}
                                        {...ESGChartConfigs.renewablesBreakdown}
                                        height={300}
                                    />
                                </Card>
                            </div>

                            {/* Row 3: REF & PUE */}
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="ref-env"
                                        type="bar"
                                        data={chartData.environment.ref}
                                        {...ESGChartConfigs.renewableEnergyFactor}
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="pue-env"
                                        type="bar"
                                        data={chartData.environment.pue}
                                        {...ESGChartConfigs.powerUsageEffectiveness}
                                        height={300}
                                    />
                                </Card>
                            </div>

                            {/* Row 4: WUE & Water Withdrawn */}
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="wue-env"
                                        type="bar"
                                        data={chartData.environment.wue}
                                        {...ESGChartConfigs.waterUsageEffectiveness}
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="water-withdrawn-env"
                                        type="bar"
                                        data={chartData.environment.waterWithdrawn}
                                        {...ESGChartConfigs.waterWithdrawn}
                                        height={300}
                                    />
                                </Card>
                            </div>

                            {/* Row 5: Non-Hazardous & Hazardous Waste */}
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="non-hazardous-waste-env"
                                        type="bar"
                                        data={chartData.environment.nonHazardousWaste}
                                        {...ESGChartConfigs.wasteGenerated}
                                        title="Non-Hazardous Waste Generated"
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="hazardous-waste-env"
                                        type="bar"
                                        data={chartData.environment.hazardousWaste}
                                        {...ESGChartConfigs.wasteGenerated}
                                        title="Hazardous Waste Generated"
                                        height={300}
                                    />
                                </Card>
                            </div>
                        </div>
                    </TabPanel>
                    
                    <TabPanel header="Social">
                        <div className="grid">
                            {/* Note: Social section is currently paused as DCF forms are not ready */}
                            <div className="col-12 mb-4">
                                <div className="card bg-yellow-50 border-yellow-200 p-4">
                                    <div className="flex align-items-center gap-3">
                                        <i className="pi pi-info-circle text-yellow-600 fs-20"></i>
                                        <div>
                                            <h5 className="text-bold fs-16 text-yellow-800 mb-2">Social Section - Development Paused</h5>
                                            <p className="fs-14 text-yellow-700 mb-0">
                                                The Social section charts are currently paused as the required DCF forms are not yet ready.
                                                This section will include metrics such as Construction & Operations Hours vs TRIR,
                                                Number of Recordable Incidents, Gender Diversity, Women in Leadership, and New Hires and Turnover.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Placeholder charts for Social section (commented out until DCF forms are ready) */}
                            {/*
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="construction-hours-social"
                                        type="bar"
                                        data={chartData.social.constructionHours}
                                        title="Construction & Operations Hours vs TRIR"
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="recordable-incidents-social"
                                        type="bar"
                                        data={chartData.social.recordableIncidents}
                                        title="Number of Recordable Incidents"
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="gender-diversity-social"
                                        type="bar"
                                        data={chartData.social.genderDiversity}
                                        title="Gender Diversity (Women %)"
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-6">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="women-leadership-social"
                                        type="bar"
                                        data={chartData.social.womenInLeadership}
                                        title="Percentage of Women in Leadership"
                                        height={300}
                                    />
                                </Card>
                            </div>
                            <div className="col-12">
                                <Card className="mb-3">
                                    <ESGChart
                                        id="new-hires-turnover-social"
                                        type="bar"
                                        data={chartData.social.newHiresAndTurnover}
                                        title="New Hires and Turnover"
                                        height={300}
                                    />
                                </Card>
                            </div>
                            */}
                        </div>
                    </TabPanel>
                    
                    <TabPanel header="Governance">
                        <div className="grid">
                            {/* Note: Governance section is currently paused as DCF forms are not ready */}
                            <div className="col-12 mb-4">
                                <div className="card bg-yellow-50 border-yellow-200 p-4">
                                    <div className="flex align-items-center gap-3">
                                        <i className="pi pi-info-circle text-yellow-600 fs-20"></i>
                                        <div>
                                            <h5 className="text-bold fs-16 text-yellow-800 mb-2">Governance Section - Development Paused</h5>
                                            <p className="fs-14 text-yellow-700 mb-0">
                                                The Governance section charts are currently paused as the required DCF forms are not yet ready.
                                                This section will include governance-related metrics such as board composition,
                                                anti-corruption practices, and other governance indicators.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Placeholder for future governance charts */}
                            {/* Charts will be added when DCF forms become available */}
                        </div>
                    </TabPanel>
                </TabView>
            </div>
        </div>
    );
};

export default ESGLiveDashboard;
